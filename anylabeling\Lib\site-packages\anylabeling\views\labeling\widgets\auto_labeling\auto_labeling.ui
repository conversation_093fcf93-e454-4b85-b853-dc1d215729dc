<?xml version="1.0" encoding="UTF-8"?>
<ui version="4.0">
 <class>auto_labeling_form</class>
 <widget class="QWidget" name="auto_labeling_form">
  <property name="geometry">
   <rect>
    <x>0</x>
    <y>0</y>
    <width>1118</width>
    <height>68</height>
   </rect>
  </property>
  <property name="sizePolicy">
   <sizepolicy hsizetype="Minimum" vsizetype="Minimum">
    <horstretch>0</horstretch>
    <verstretch>0</verstretch>
   </sizepolicy>
  </property>
  <property name="windowTitle">
   <string>Form</string>
  </property>
  <property name="styleSheet">
   <string notr="true"/>
  </property>
  <layout class="QVBoxLayout" name="verticalLayout">
   <property name="spacing">
    <number>0</number>
   </property>
   <property name="leftMargin">
    <number>2</number>
   </property>
   <property name="topMargin">
    <number>2</number>
   </property>
   <property name="rightMargin">
    <number>2</number>
   </property>
   <property name="bottomMargin">
    <number>2</number>
   </property>
   <item>
    <layout class="QHBoxLayout" name="model_selection">
     <property name="spacing">
      <number>2</number>
     </property>
     <property name="leftMargin">
      <number>4</number>
     </property>
     <property name="topMargin">
      <number>0</number>
     </property>
     <item>
      <widget class="QLabel" name="model_label">
       <property name="text">
        <string>Auto</string>
       </property>
      </widget>
     </item>
     <item>
      <widget class="QComboBox" name="model_select_combobox">
       <property name="enabled">
        <bool>true</bool>
       </property>
       <property name="minimumSize">
        <size>
         <width>200</width>
         <height>0</height>
        </size>
       </property>
       <item>
        <property name="text">
         <string>No Model</string>
        </property>
       </item>
      </widget>
     </item>
     <item>
      <widget class="QLabel" name="output_label">
       <property name="text">
        <string>Output</string>
       </property>
      </widget>
     </item>
     <item>
      <widget class="QComboBox" name="output_select_combobox">
       <item>
        <property name="text">
         <string>polygon</string>
        </property>
       </item>
       <item>
        <property name="text">
         <string>rectangle</string>
        </property>
       </item>
      </widget>
     </item>
     <item>
      <widget class="Line" name="line">
       <property name="orientation">
        <enum>Qt::Vertical</enum>
       </property>
      </widget>
     </item>
     <item>
      <widget class="QPushButton" name="button_run">
       <property name="text">
        <string>Run (i)</string>
       </property>
      </widget>
     </item>
     <item>
      <widget class="QPushButton" name="button_add_point">
       <property name="text">
        <string>+Point (Q)</string>
       </property>
      </widget>
     </item>
     <item>
      <widget class="QPushButton" name="button_remove_point">
       <property name="text">
        <string>-Point (E)</string>
       </property>
      </widget>
     </item>
     <item>
      <widget class="QPushButton" name="button_add_rect">
       <property name="text">
        <string>+Rect</string>
       </property>
      </widget>
     </item>
     <item>
      <widget class="QPushButton" name="button_clear">
       <property name="text">
        <string>Clear</string>
       </property>
      </widget>
     </item>
     <item>
      <widget class="QPushButton" name="button_finish_object">
       <property name="text">
        <string>Finish Object (f)</string>
       </property>
      </widget>
     </item>
     <item>
      <spacer name="horizontalSpacer">
       <property name="orientation">
        <enum>Qt::Horizontal</enum>
       </property>
       <property name="sizeHint" stdset="0">
        <size>
         <width>0</width>
         <height>0</height>
        </size>
       </property>
      </spacer>
     </item>
     <item>
      <widget class="QPushButton" name="button_close">
       <property name="sizePolicy">
        <sizepolicy hsizetype="Minimum" vsizetype="Fixed">
         <horstretch>0</horstretch>
         <verstretch>0</verstretch>
        </sizepolicy>
       </property>
       <property name="text">
        <string/>
       </property>
       <property name="icon">
        <iconset resource="../../../../resources/resources.qrc">
         <normaloff>:/images/images/cancel.png</normaloff>:/images/images/cancel.png</iconset>
       </property>
       <property name="iconSize">
        <size>
         <width>12</width>
         <height>12</height>
        </size>
       </property>
      </widget>
     </item>
    </layout>
   </item>
   <item>
    <widget class="QLabel" name="model_status_label">
     <property name="sizePolicy">
      <sizepolicy hsizetype="Expanding" vsizetype="Minimum">
       <horstretch>0</horstretch>
       <verstretch>0</verstretch>
      </sizepolicy>
     </property>
     <property name="font">
      <font>
       <pointsize>10</pointsize>
      </font>
     </property>
     <property name="styleSheet">
      <string notr="true">margin-top: 0;
margin-bottom: 10px;</string>
     </property>
     <property name="text">
      <string>Ready!</string>
     </property>
    </widget>
   </item>
  </layout>
 </widget>
 <resources>
  <include location="../../../../resources/resources.qrc"/>
 </resources>
 <connections/>
</ui>
