Metadata-Version: 2.4
Name: anylabeling
Version: 0.4.30
Summary: Effortless data labeling with AI support
Home-page: https://github.com/vietanhdev/anylabeling
Author: V<PERSON><PERSON><PERSON><PERSON>
Author-email: <EMAIL>
License: GPLv3
Keywords: Image Annotation,Machine Learning,Deep Learning
Classifier: Intended Audience :: Developers
Classifier: Intended Audience :: Science/Research
Classifier: Natural Language :: English
Classifier: Operating System :: OS Independent
Classifier: Programming Language :: Python
Classifier: Programming Language :: Python :: 3.10
Classifier: Programming Language :: Python :: 3.11
Classifier: Programming Language :: Python :: 3.12
Classifier: Programming Language :: Python :: 3 :: Only
Description-Content-Type: text/markdown
License-File: LICENSE
Requires-Dist: imgviz>=0.11
Requires-Dist: natsort>=7.1.0
Requires-Dist: numpy==1.26.4
Requires-Dist: Pillow>=2.8
Requires-Dist: PyYAML==6.0.1
Requires-Dist: termcolor==1.1.0
Requires-Dist: opencv-python-headless==********
Requires-Dist: PyQt5>=5.15.7; platform_system != "Darwin"
Requires-Dist: onnx==1.16.1
Requires-Dist: qimage2ndarray==1.10.0
Requires-Dist: darkdetect==0.8.0
Requires-Dist: onnxruntime==1.18.1
Dynamic: author
Dynamic: author-email
Dynamic: classifier
Dynamic: description
Dynamic: description-content-type
Dynamic: home-page
Dynamic: keywords
Dynamic: license
Dynamic: license-file
Dynamic: requires-dist
Dynamic: summary

<p align="center">
  <img alt="AnyLabeling" style="width: 128px; height: 128px; height: auto;" src="https://github.com/user-attachments/assets/847e47e6-acf0-4f96-9ed9-5485ab405ae0"/>
  <h1 align="center">🌟 AnyLabeling 🌟</h1>
  <p align="center">Effortless data labeling with AI support from <b>YOLO</b> and <b>Segment Anything</b>!</p>
  <p align="center"><b>AnyLabeling = LabelImg + Labelme + Improved UI + Auto-labeling</b></p>
</p>

![](https://user-images.githubusercontent.com/18329471/234640541-a6a65fbc-d7a5-4ec3-9b65-55305b01a7aa.png)

[![PyPI](https://img.shields.io/pypi/v/anylabeling)](https://pypi.org/project/anylabeling)
[![license](https://img.shields.io/github/license/vietanhdev/anylabeling.svg)](https://github.com/vietanhdev/anylabeling/blob/master/LICENSE)
[![open issues](https://isitmaintained.com/badge/open/vietanhdev/anylabeling.svg)](https://github.com/vietanhdev/anylabeling/issues)
[![Pypi Downloads](https://pepy.tech/badge/anylabeling)](https://pypi.org/project/anylabeling/)
[![Documentation](https://img.shields.io/badge/Read-Documentation-green)](https://anylabeling.nrl.ai/)
[![Follow](https://img.shields.io/badge/+Follow-vietanhdev-blue)]([[https://anylabeling.nrl.ai/](https://twitter.com/vietanhdev)](https://twitter.com/vietanhdev))

[![AnyLearning-Banner](https://github.com/user-attachments/assets/c2de3534-3e04-439b-bdca-19f6fcb9fc61)](https://anylearning.nrl.ai/)

[![ai-flow 62b3c222](https://github.com/user-attachments/assets/a47a0eea-ec59-4c59-9733-737b1977e56b)](https://anylearning.nrl.ai/)


<a href="https://youtu.be/5qVJiYNX5Kk">
  <img alt="AnyLabeling" src="https://raw.githubusercontent.com/vietanhdev/anylabeling/master/assets/screenshot.png"/>
</a>

**Auto Labeling with Segment Anything**

<a href="https://youtu.be/5qVJiYNX5Kk">
  <img style="width: 800px; margin-left: auto; margin-right: auto; display: block;" alt="AnyLabeling-SegmentAnything" src="https://user-images.githubusercontent.com/18329471/236625792-07f01838-3f69-48b0-a12e-30bad27bd921.gif"/>
</a>


- **Youtube Demo:** [https://www.youtube.com/watch?v=5qVJiYNX5Kk](https://www.youtube.com/watch?v=5qVJiYNX5Kk)
- **Documentation:** [https://anylabeling.nrl.ai](https://anylabeling.nrl.ai)

**Features:**

- [x] Image annotation for polygon, rectangle, circle, line and point.
- [x] Auto-labeling YOLOv8, Segment Anything (SAM, SAM2).
- [x] Text detection, recognition and KIE (Key Information Extraction) labeling.
- [x] Multiple languages availables: English, Vietnamese, Chinese.

## Install and Run

### 1. Download and run executable

- Download and run newest version from [Releases](https://github.com/vietanhdev/anylabeling/releases).
- For MacOS:
  - Download the folder mode build (`AnyLabeling-Folder.zip`) from [Releases](https://github.com/vietanhdev/anylabeling/releases)
  - See [macOS folder mode instructions](docs/macos_folder_mode.md) for details

### Install from Pypi

- Requirements: Python 3.10+. Recommended: Python 3.12.
- Recommended: [Miniconda/Anaconda](https://docs.conda.io/en/latest/miniconda.html).

- Create environment:

```bash
conda create -n anylabeling python=3.12
conda activate anylabeling
```

- **(For macOS only)** Install PyQt5 using Conda:

```bash
conda install -c conda-forge pyqt==5.15.9
```

- Install anylabeling:

```bash
pip install anylabeling # or pip install anylabeling-gpu for GPU support
```

- Start labeling:

```bash
anylabeling
```

## Documentation

**Website:** [https://anylabeling.nrl.ai](https://anylabeling.nrl.ai)/

### Applications

| **Object Detection** | **Recognition** | **Facial Landmark Detection** | **2D Pose Estimation** |
| :---: | :---: | :---: | :---: |
| <img src='https://user-images.githubusercontent.com/72010077/273488633-fc31da5c-dfdd-434e-b5d0-874892807d95.png' height="126px" width="180px"> |  <img src='https://user-images.githubusercontent.com/72010077/277396071-79daec2c-6b0a-4d42-97cf-69fd098b3400.png' height="126px" width="180px"> |  <img src='https://user-images.githubusercontent.com/61035602/206095684-72f42233-c9c7-4bd8-9195-e34859bd08bf.jpg' height="126px" width="180px"> | <img src='https://user-images.githubusercontent.com/61035602/206100220-ab01d347-9ff9-4f17-9718-290ec14d4205.gif' height="126px" width="180px"> |
|  **2D Lane Detection** | **OCR** | **Medical Imaging** | **Instance Segmentation** |
| <img src='https://user-images.githubusercontent.com/72010077/273764641-65f456ed-27ce-4077-8fce-b30db093b988.jpg' height="126px" width="180px"> | <img src='https://user-images.githubusercontent.com/72010077/273421210-30d20e08-3b72-4f4d-8976-05b564e13d87.png' height="126px" width="180px"> | <img src='https://user-images.githubusercontent.com/72010077/273764318-e8b6a197-e733-478e-a210-e4386bafa1e4.png' height="126px" width="180px"> | <img src='https://user-images.githubusercontent.com/61035602/206095831-cc439557-1a23-4a99-b6b0-b6f2e97e8c57.jpg' height="126px" width="180px"> |
|  **Image Tagging** | **Rotation** | **And more!** |
| <img src='https://user-images.githubusercontent.com/72010077/277670825-8797ac7e-e593-45ea-be6a-65c3af17b12b.png' height="126px" width="180px"> | <img src='https://user-images.githubusercontent.com/72010077/277395955-aab54ea0-88f5-41af-ab0a-f4158a673f5e.png' height="126px" width="180px"> | Your applications here! |
## Development

- Install packages:

```bash
pip install -r requirements-dev.txt
# or pip install -r requirements-macos-dev.txt for MacOS
```

- Generate resources:

```bash
pyrcc5 -o anylabeling/resources/resources.py anylabeling/resources/resources.qrc
```

- Run app:

```bash
python anylabeling/app.py
```

## Build executable

- Install PyInstaller:

```bash
pip install -r requirements-dev.txt
```

- Build:

```bash
bash build_executable.sh
```

- Check the outputs in: `dist/`.

## Contribution

If you want to contribute to **AnyLabeling**, please read [Contribution Guidelines](https://anylabeling.nrl.ai/docs/contribution).

## Star history

[![Star History Chart](https://api.star-history.com/svg?repos=vietanhdev/anylabeling&type=Date)](https://star-history.com/#vietanhdev/anylabeling&Date)

## References

- Labeling UI built with ideas and components from [LabelImg](https://github.com/heartexlabs/labelImg), [LabelMe](https://github.com/wkentaro/labelme).
- Auto-labeling with [Segment Anything Models](https://segment-anything.com/), [MobileSAM](https://github.com/ChaoningZhang/MobileSAM).
- Auto-labeling with [YOLOv8](https://github.com/ultralytics/ultralytics).
- Icons from FlatIcon: [DinosoftLabs](https://www.flaticon.com/free-icons/sun "sun icons"), [Freepik](https://www.flaticon.com/free-icons/moon "moon icons"), [Vectoricons](https://www.flaticon.com/free-icons/system "system icons"), [HideMaru](https://www.flaticon.com/free-icons/ungroup "ungroup icons").
